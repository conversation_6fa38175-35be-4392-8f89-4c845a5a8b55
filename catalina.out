08-Sep-2025 12:35:45.344 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version:        Apache Tomcat/8.5.13
08-Sep-2025 12:35:45.351 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Mar 27 2017 14:25:04 UTC
08-Sep-2025 12:35:45.351 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server number:         8.5.13.0
08-Sep-2025 12:35:45.352 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Linux
08-Sep-2025 12:35:45.353 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            6.1.129-138.220.amzn2023.x86_64
08-Sep-2025 12:35:45.353 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
08-Sep-2025 12:35:45.354 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             /usr/lib/jvm/java-1.8.0-amazon-corretto.x86_64/jre
08-Sep-2025 12:35:45.355 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           1.8.0_462-b08
08-Sep-2025 12:35:45.355 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            Amazon.com Inc.
08-Sep-2025 12:35:45.356 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         /u01/apache-tomcat-8.5.13
08-Sep-2025 12:35:45.356 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         /u01/apache-tomcat-8.5.13
08-Sep-2025 12:35:45.357 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=/u01/apache-tomcat-8.5.13/conf/logging.properties
08-Sep-2025 12:35:45.357 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
08-Sep-2025 12:35:45.358 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
08-Sep-2025 12:35:45.358 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
08-Sep-2025 12:35:45.359 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=/u01/apache-tomcat-8.5.13
08-Sep-2025 12:35:45.360 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=/u01/apache-tomcat-8.5.13
08-Sep-2025 12:35:45.360 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=/u01/apache-tomcat-8.5.13/temp
08-Sep-2025 12:35:45.361 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: /usr/java/packages/lib/amd64:/usr/lib64:/lib64:/lib:/usr/lib
08-Sep-2025 12:35:45.547 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["ajp-nio-8009"]
08-Sep-2025 12:35:45.592 INFO [main] org.apache.tomcat.util.net.NioSelectorPool.getSharedSelector Using a shared selector for servlet write/read
08-Sep-2025 12:35:45.598 INFO [main] org.apache.catalina.startup.Catalina.load Initialization processed in 1253 ms
08-Sep-2025 12:35:45.647 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service Catalina
08-Sep-2025 12:35:45.647 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet Engine: Apache Tomcat/8.5.13
08-Sep-2025 12:35:45.661 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployDescriptor Deploying configuration descriptor /u01/apache-tomcat-8.5.13/conf/Catalina/ws/ROOT.xml
08-Sep-2025 12:35:45.728 INFO [ws-startStop-1] org.apache.catalina.startup.ExpandWar.expand An expanded directory [/u01/apache-tomcat-8.5.13/webapps/ROOT] was found with a last modified time that did not match the associated WAR. It will be deleted.
08-Sep-2025 12:35:59.101 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_displayChapterDetails_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.104 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_flashCardSection_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.105 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_footer_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.113 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_footer_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.115 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_gradeSelector_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.116 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_gradeSelector_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.116 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_gradeSelectorScript_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.117 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_gradeSelectorScript_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.118 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_loginChecker_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.118 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_loginChecker_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.119 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_materialTabs_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.120 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_materialTabs_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.121 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_navheader_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.121 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_navheader_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.122 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_navheader_new_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.123 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_navheader_new_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.124 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_notesHighlightSection_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.125 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_notesHighlightSection_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.126 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_qaSection_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.127 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_qaSection_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.127 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_quizSection_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.128 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_quizSection_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.129 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_readerView_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.129 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_readerView_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.130 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_readingSection_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.131 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_readingSection_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.131 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_reviewrating_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.133 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmain_aiOptions_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.134 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_reviewrating_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.134 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_revision_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.135 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_revision_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.136 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_slider_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.137 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_slider_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.137 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_testgenModal_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.138 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_testgenModal_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.139 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_testgeneratorscripts_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.139 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_testgeneratorscripts_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.140 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_uploadResourcesModal_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.141 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_uploadResourcesModal_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.142 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_videoPlay_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.142 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_videoPlay_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.143 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_videoSection_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.143 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_videoSection_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.144 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_weblinksSection_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.145 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_weblinksSection_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.145 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_wonderGoaStudySet_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.146 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_wonderGoaStudySet_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.146 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_wsFooter_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.147 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_wsFooter_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.148 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishanswerFixerInput_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.148 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishanswerFixerInput_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.149 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishanswerFixerProcessor_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.149 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishanswerFixerProcessor_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.150 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishanswerMatch_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.151 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishanswerMatch_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.151 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublisharihant_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.152 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublisharihant_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.152 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishauthorDetails_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.153 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishauthorDetails_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.154 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbannerManagement_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.154 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbannerManagement_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.155 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbook_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.155 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbook_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.156 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbookCreate_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.157 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbookCreate_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.158 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbookCreateNew_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.158 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbookCreateNew_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.159 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbookdtl_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.160 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbookdtl_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.160 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishdeleteAccount_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.161 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishdeleteAccount_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.161 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishdemoHome_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.162 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishdemoHome_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.163 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublisherrorPage_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.163 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublisherrorPage_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.164 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishindependentContent_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.164 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishindependentContent_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.165 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishindex_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.166 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishindex_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.166 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishindexHome_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.167 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishindexHome_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.167 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishinstructorLibrary_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.168 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishinstructorLibrary_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.168 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmanageExams_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.169 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmanageExams_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.170 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmanageTabs_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.170 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmanageTabs_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.170 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmybooks_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.171 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmybooks_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.173 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmylibrary_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.173 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmylibrary_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.174 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishnotescreator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.175 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishnotescreator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.175 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishopenPDF_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.176 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishopenPDF_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.177 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishorders_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.177 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishorders_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.178 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishpdfReader_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.179 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishpdfReader_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.179 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishpubDesk_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.180 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishpubDesk_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.181 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishpubSales_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.181 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishpubSales_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.182 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishqandaCreator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.182 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishqandaCreator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.183 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishquizcreator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.183 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishquizcreator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.183 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishquizcreatorbulk_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.184 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishquizcreatorbulk_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.184 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishquizcreatorbulkinput_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.185 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishquizcreatorbulkinput_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.185 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishrelatedVideosAdmin_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.186 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishrelatedVideosAdmin_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.186 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishresetPasswordEmail_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.186 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishresetPasswordEmail_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.187 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishselfService_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.188 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishselfService_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.188 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishstudySet_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.189 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishstudySet_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.189 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishtestgenerator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.189 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishtestgenerator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.190 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishuploadTex_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.190 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishuploadTex_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.191 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishvideoExplanation_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.191 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishvideoExplanation_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.192 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishvideoplayer_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.192 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishvideoplayer_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.193 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishwseditor_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.193 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishwseditor_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.194 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderslatekidsindex_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.194 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderslatekidsindex_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.195 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmain_aiContent_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.195 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmain_aiContent_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.196 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmain_aiOptions_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.196 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainaiBookDtl_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.197 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainaiBookDtl_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.197 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainaibook_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.197 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainaibook_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.198 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainbookai_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.198 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainbookai_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.199 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainindex_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.199 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainindex_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.200 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainmockpapercreator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.200 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainmockpapercreator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.201 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainmockpaperlist_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.201 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainmockpaperlist_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.202 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainmyLibrary_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.202 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainmyLibrary_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.203 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainqpcreator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.203 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainqpcreator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.204 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainqplist_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.204 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainqplist_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.205 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainqpprint_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.205 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainqpprint_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.206 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainqpview_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.206 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainqpview_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.207 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainunmarkedQA_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.207 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainunmarkedQA_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.208 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsLibraryaccessCode_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.208 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsLibraryaccessCode_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.208 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsLibraryindex_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.209 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsLibraryindex_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.209 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsLibrarymyLibrary_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.210 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsLibrarymyLibrary_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.210 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_blogEnglishDisplay_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.211 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_blogEnglishDisplay_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.211 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_cartScripts_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.211 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_cartScripts_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.212 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_searchScripts_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.212 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_searchScripts_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.213 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_shoppingCartLogic_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.213 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_shoppingCartLogic_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.214 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_videoCreator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.214 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_videoCreator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.214 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopaddGradeInfo_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.215 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopaddGradeInfo_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.215 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopamazonorderconfirmation_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.216 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopamazonorderconfirmation_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.216 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopblogEnglish_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.216 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopblogEnglish_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.217 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopblogHindi_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.217 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopblogHindi_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.218 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopcart_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.218 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopcart_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.218 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopmanageShopSpecials_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.219 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopmanageShopSpecials_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.219 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshoporderManagement_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.220 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshoporderManagement_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.220 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopvalidityExtensionAdmin_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.221 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopvalidityExtensionAdmin_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.222 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.222 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/ibookso] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.225 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/usermanagement] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.226 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/discussion] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.226 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/admin] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.227 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/librarybooks] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.227 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/institute] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.227 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/logs] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.230 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.231 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/cache] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.231 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/groups] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.231 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/toDo] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.232 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/shop] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.232 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/comparison] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.233 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/WsLibrary] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.233 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/drive] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.233 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/qp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.234 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/report] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.234 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/log] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.235 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/prepjoy] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.235 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/harper] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.236 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/seo] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.236 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/learn] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.236 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/information] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.237 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/publish] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.237 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/discussions] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.237 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/publiclibrary] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.238 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/content] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.238 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/games] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.262 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/view] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.275 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/client] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.285 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/marketing] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:35:59.290 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/sqlutil] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.301 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/ibookso/products] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.317 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.320 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/folder/folderHome.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.326 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/libwonder/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.327 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/libwonder/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.327 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/libwonder/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.329 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/pdfReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.330 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_allSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.330 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_notesHighlightSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.330 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_printBookSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.331 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_resourceContents.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.331 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_security.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.331 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/createFlashCards.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.332 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/mobilePdfReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.332 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/contactus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.332 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_ebookInclude.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.332 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/ebook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.333 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_videoSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.333 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_shareContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.333 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/displayFlashCards.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.334 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_wsOtherResource.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.334 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_bookTestSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.334 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/notesViewer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.342 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_weblinksSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.342 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_flashcardMatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.343 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_revision.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.343 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_displayChapterDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.343 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/quizcreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.344 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_relatedBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.344 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/flpDemo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.344 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/displayPdfMaterial.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.346 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_resourceChapterList.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.348 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/book.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.348 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/flashcardHome.css] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.349 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_cartScripts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.349 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.349 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_readSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.349 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/deleteChapters.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.350 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/flashCardHome.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.350 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/eBookDtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.350 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/createPdfBook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.351 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_eBookSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.351 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_mobileResourceMenu.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.351 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_collectionBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.351 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_genericReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.352 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_readerView.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.352 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_qaSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.352 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_eBookSection_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.353 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/previewChapter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.353 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_ebookFeatures.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.355 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/editQuiz.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.355 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/pdfViewer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.356 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/epubReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.356 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_bookGPTSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.356 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/robots.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.357 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/childBookInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.358 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/discountManager.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.358 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/searchDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.358 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/printBooksDownloadPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.359 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/videoExplanationUpdate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.359 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/informationPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.359 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/refundDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.359 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/managePublishers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.360 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/accessCodeUsage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.360 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/jwplayerLive.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.360 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/cartPurchase.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.361 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/list.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.361 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/contentModeration.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.362 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/publisherDsize.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.362 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/dataRetriever.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.362 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/informationType.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.364 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/manageBatchUsers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.365 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/priceList.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.365 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/userBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.366 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/_pomodoro.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.367 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/manageBookExpiry.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.367 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/insertjob.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.367 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/manageNotifications.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.368 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/emailWhitelist.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.368 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/externalOrders.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.368 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/moderation.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.369 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/deleteuser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.369 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/informationPageDtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.369 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/paymentDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.370 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/bookgptPromptAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.370 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/_bookgptChatViewer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.370 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/_bookgptPDFViewer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.371 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/difficultyLevelMapping.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.371 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/bookSupport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.372 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/salesDashboard/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.373 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/appVersionManagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.373 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/userAccess.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.373 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/unblockUser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.374 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/quizissues.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.374 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/notificationManagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.374 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/liveTestRanks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.375 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/userManagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.375 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/enquiryDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.376 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/enquiryForm.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.376 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/packageBooksReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.376 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/deleteUserBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.376 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/notification.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.377 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/migrateuser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.377 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/karnataka.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.378 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/dailyCurrentAffairs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.378 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/cacscma.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.378 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/ctet.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.379 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.379 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/prepJoyNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.380 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/enggentrances.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.380 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/quiz.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.380 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.380 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/affiliation.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.381 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.381 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/dailyTest.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.382 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/currentaffairs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.382 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/quizAnalytics.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.382 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/leaderBoard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.382 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/_prepjoySignup.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.383 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/currentAffairsDaily.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.383 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/currentAffairs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.384 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/neet.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.384 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/eBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.384 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/history.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.385 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/joinGame.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.385 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/prepJoyGame.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.385 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/audioChat.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.386 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/_prepjoy-loader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.386 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/join.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.386 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/creator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.387 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/admin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.387 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/_quizResult.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.388 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/_instituteBanners.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.389 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/usageReportInstituteAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.389 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/usageReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.389 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/ntse.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.390 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/manageInstitutePage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.390 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/isbnKeyword.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.390 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/home.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.391 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/userLoginLimit.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.391 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/admin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.391 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/usageReportDoris.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.392 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.392 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/userManagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.392 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/manageClasses.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.393 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/registeredUserReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.393 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/libAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.393 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/recentInstitutes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.394 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/libraryUserUploader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.394 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/usageReportTitleWise.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.394 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/downloadUsageReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.395 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/bookCreateNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.395 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/usageReportCorporateWise.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.395 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/instituteProduct.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.396 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.397 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.397 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/privacy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.397 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.398 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/_storeHolder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.398 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/_commonfooter_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.398 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/store1.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.399 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/contact.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.399 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/about.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.399 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.399 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.400 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.400 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/termsCondition.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.400 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/library.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.401 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/terms.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.402 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_storeDisplay.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.402 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_billingShipping.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.402 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_shoppingCartDisplay.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.403 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_storeLogic.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.403 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/migrateBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.403 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/recharge.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.404 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/setBookType.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.404 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_aiStoreLogic.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.404 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_aiStoreDisplay.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.405 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_shoppingCartLogic.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.405 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wolterskluwer/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.406 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wolterskluwer/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.406 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wolterskluwer/_storeHolder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.406 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wolterskluwer/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.407 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wolterskluwer/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.407 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wolterskluwer/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.407 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mtg/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.408 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mtg/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.408 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mtg/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.408 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mtg/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.409 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/appinapp/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.409 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/appinapp/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.410 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/appinapp/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.410 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswaal/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.411 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswaal/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.411 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswaal/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.411 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswaal/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.412 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/wrongPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.412 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/packageBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.413 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/faq.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.413 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/eduWonder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.413 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_home_reportSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.413 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/schoolProducts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.414 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/sageLanding.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.414 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/books.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.414 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.415 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/description.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.415 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_leaderBoard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.415 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/shoppingCart.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.416 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/home.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.416 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_moreOptions.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.416 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/ebooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.417 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.417 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/contactus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.417 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/ebook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.417 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_shareContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.418 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_signup_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.418 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/vtulibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.418 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/vtuanalytics.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.419 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.419 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/toppers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.419 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/informationPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.420 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_home_latestQuiz.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.420 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/eBooksStoreIntegration.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.420 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/mylibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.420 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_wsabout.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.421 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/myHome.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.421 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/careercounselling.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.421 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.422 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/book.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.422 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_signIn.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.422 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_home_sideBar.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.423 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_login.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.423 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.423 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/aboutus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.424 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.424 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_home_institutes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.424 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_home_searchSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.425 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/landingPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.425 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/bookdtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.425 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/checkout.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.426 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/dashboard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.426 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_bannerSlider.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.426 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/leaderboard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.426 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_pomodoro.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.427 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/directSales.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.427 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/appLink.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.427 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/vtubook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.428 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/myActivity.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.428 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_bfooter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.428 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/userOrderProcess.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.429 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/bookCreateNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.429 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/products.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.429 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_home_continueLearning.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.429 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/publishersProduct.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.430 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/informationPageDtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.430 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/vtu.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.430 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/teachersProduct.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.431 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/vtuadmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.431 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/studentproblems.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.431 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/manageExams.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.432 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/pdftoepub.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.432 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/blogs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.432 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_ibookgpt-promotion.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.434 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sageUI/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.434 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sageUI/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.435 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sageUI/authorDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.435 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sageUI/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.436 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.436 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/myLibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.436 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/unmarkedQA.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.436 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/_aiOptions.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.437 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/bookai.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.437 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/aibook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.437 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/aiBookDtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.438 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/_aiContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.438 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/qplist.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.438 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/qpprint.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.439 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/qpview.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.439 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/qpcreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.439 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/mockpaperlist.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.440 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/mockpapercreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.440 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autoVideo/automatedVideo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.441 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autoVideo/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.441 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autoVideo/autoImageVideo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.442 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/resetPassword.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.442 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/instituteWelcomeEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.442 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/etextsSendContactUs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.443 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/welcomeModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.443 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/invoice2020.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.443 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/ebouquetUser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.443 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_welcomeModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.444 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/ccavRequestHandler.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.444 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_pageupload.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.444 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/radianBooksInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.445 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/wsinvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.445 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/winnersInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.445 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/wolterskluwerSuggestbookemail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.446 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userCreationEmailSage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.446 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/otpRequestEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.446 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_loggedactivities.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.446 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/otpRequestEmailArihant.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.447 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/edugorillaInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.447 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/otpRequestEmailLibwonder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.447 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/etextsSendFeedback.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.448 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userCreationEmailEbouquet.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.448 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userCreationEmailEvidya.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.448 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/resetPasswordEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.448 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/wsuser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.449 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userBookPurchaseEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.449 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/blackspineInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.449 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/ebouquetSuggestbookemail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.450 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/etextsSuggestbookemail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.450 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.450 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/resetPasswordEmailEtexts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.451 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/ebouquetSuggestbookemailuser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.451 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userActiveCartEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.451 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/otpRequestEmailEbouquet.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.451 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/authordetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.452 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userProfile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.452 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/instituteUserEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.453 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userPaidPreviewEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.453 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/jbclassInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.453 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/otpRequestEmailSage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.453 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/paymentPendingEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.454 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userCreationEmailEtexts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.454 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/enquiryFormEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.454 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/ebouquetRequestDemo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.455 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_pageaddquiz.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.455 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_register.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.455 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/displayChapter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.455 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/contentModerationEmailWS.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.456 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/mtgInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.456 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/oswaalInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.456 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userPurchasedBookEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.457 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/affiliationEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.457 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/list.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.457 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/evidyaSuggestbookemail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.457 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userBookPurchase.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.458 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/editProfile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.458 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_addTopic.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.458 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_invoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.459 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/chapterDownloadError.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.459 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_changePasswordModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.459 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/invoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.459 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/returnPolicy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.460 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/addResource.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.460 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_userProfile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.460 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/oswalpublisherInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.461 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/ebouquetRetriever.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.461 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/externalPurchaseMail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.461 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/etextsSuggestbookemailuser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.461 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_pageaddlink.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.462 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/resetPasswordEmailSage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.462 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/evidyaSuggestbookemailuser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.462 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/approveContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.463 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_userProfile_prepjoy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.463 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userCreationEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.463 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_contentCreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.464 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_userProfileSage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.464 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/arihantInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.464 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/addUser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.464 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/privateLabelInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.465 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/prepjoyInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.465 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/resetPasswordEmailEvidya.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.465 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_userProfile_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.466 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/purchaseConfirmationEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.466 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/appInAppInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.466 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/otpAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.468 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/bookPrice/_priceManager.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.468 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswalpublisher/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.469 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswalpublisher/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.469 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswalpublisher/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.469 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswalpublisher/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.470 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/deliveryCharges/manageDeliveryCharges.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.470 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/printbooks/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.471 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/printbooks/_categories.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.471 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/printbooks/printbooksmanagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.471 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/printbooks/_printSearch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.471 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mail/activitiesUpdate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.472 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mail/printbooks] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.472 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mail/sendInvite.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.472 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mail/printbooks/printbooksmanagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.473 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/pageManager.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.473 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.473 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/admin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.473 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/allWebsites.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.474 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/contactus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.474 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.474 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/ibookso.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.475 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/customPageCreation.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.475 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.475 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/_otpOnlyLogin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.475 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.476 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/_ibooksoBanner.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.476 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/createNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.476 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/wileySignup.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.477 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/customPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.477 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/deleteAccount.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.477 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/cuetAcademics.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.478 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/loginPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.478 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/knimbus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.478 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/cart.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.478 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/gptsir.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.479 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/accessCodeBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.479 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/addSite.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.479 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/listSites.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.480 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/aiStore.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.480 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/radianbooks/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.481 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/radianbooks/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.481 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/radianbooks/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.481 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/radianbooks/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.482 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_mobileSignup.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.482 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_plindex.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.483 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/notescreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.483 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_wpfooter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.483 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_mobileLogin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.483 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_topicscripts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.484 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/flpReportDownload.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.484 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/profile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.484 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/home.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.485 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/success.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.485 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.485 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_mcq.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.485 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/signUp.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.486 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/privacy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.486 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.486 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_mainheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.486 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_tandc.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.487 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_topicdisplay.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.487 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/findFriends.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.487 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/mylibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.488 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_mcqalt.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.488 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/forgotpassword.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.488 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/quizcreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.488 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/topic.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.489 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/groupdtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.489 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/facebook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.489 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/quiz.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.490 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/signIn.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.490 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_mta.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.490 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/book.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.490 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_chaptersModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.491 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/tour.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.491 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_signIn.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.491 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_wsindex.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.492 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/recent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.492 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_topicinclude.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.492 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/test.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.492 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/aboutus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.493 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.493 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/futureLearningProgram.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.493 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/groups.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.494 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/careers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.494 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/termsandconditions.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.494 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_tof.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.494 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_fib.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.495 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_opp.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.495 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_signupNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.495 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_pnavheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.496 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/renderContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.496 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/renderMCQContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.496 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/deleteAccount.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.497 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/doubts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.498 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.498 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.498 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/reports.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.498 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/classes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.499 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/students.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.499 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.499 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/teachers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.500 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/postDetail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.500 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/groupReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.500 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/members.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.501 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/groupDtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.501 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.501 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/_signIn.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.501 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/groupCreate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.502 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/reported.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.502 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/memberRequests.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.503 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderslatekids/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.503 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/toDo/toDoListAndUpdate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.503 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/testgenerator/_testgen.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.504 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/testgenerator/monthlyQuiz.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.504 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepJoy/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.504 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepJoy/prepJoyNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.505 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepJoy/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.505 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepJoy/currentAffairsDaily.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.505 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepJoy/currentAffairs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.506 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/analytics/reportDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.506 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/analytics/leaderBoardForAdmins.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.506 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/support/getAllPayments.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.507 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/support/addUser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.507 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/support/findScratchCode.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.508 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wlibrary/library.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.508 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/validityExtensionAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.508 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/cart.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.508 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/amazonorderconfirmation.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.509 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/_cartScripts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.509 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/_searchScripts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.509 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/manageShopSpecials.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.510 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/orderManagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.510 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/_shoppingCartLogic.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.510 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/blogEnglish.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.510 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/blogHindi.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.511 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/addGradeInfo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.511 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/_videoCreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.511 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/_blogEnglishDisplay.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.512 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/messaging/messages.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.512 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/discussionBoardAdmin/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.513 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.513 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.513 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/_tandc.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.513 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/_wsabout.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.514 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/contact.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.514 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.514 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/eutkarshrefund.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.515 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.515 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.515 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/eutkarshprivacy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.515 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/eutkarshterms.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.516 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/intelligence/demo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.516 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/intelligence/admin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.517 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/intelligence/gptContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.517 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/intelligence/bookChapters.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.517 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/intelligence/chat.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.518 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_gradeSelector.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.518 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_addnotes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.518 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/pdfReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.519 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_bookdetailsTab.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.519 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/notescreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.519 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_allSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.519 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_notesHighlightSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.520 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/mybooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.520 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/instructorLibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.520 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/bookCreate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.521 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_testgenModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.521 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_materialTabs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.521 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/openPDF.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.521 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/demoHome.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.522 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_bookReviewModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.522 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_testgeneratorscripts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.522 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.523 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_answerMatchModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.523 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_additionalReferences.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.523 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/resetPasswordEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.524 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/uploadTex.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.524 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_wsFooter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.524 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_videoSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.525 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/wseditor.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.525 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.525 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_weblinksSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.525 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/orders.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.526 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/qandaCreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.526 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_commonfooter_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.526 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/bannerManagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.526 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_loginChecker.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.527 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_revision.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.527 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_displayChapterDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.527 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/mylibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.528 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/pubDesk.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.528 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/quizcreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.528 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_addNotesScripts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.528 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_additionalReferenceSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.529 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_bookanalytics.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.529 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_bookChaptersTab.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.529 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/indexHome.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.530 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_quizSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.530 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/authorDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.530 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_bookReviews.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.530 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/book.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.531 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_flashCardSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.531 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_commonfooter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.531 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/answerFixerInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.532 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/selfService.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.532 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/answerFixerProcessor.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.532 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_buyOrAdd.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.533 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/relatedVideosAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.533 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/testgenerator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.533 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/answerMatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.533 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.534 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_discussform.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.534 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.534 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_uploadResourcesModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.535 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_gradeSelectorScript.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.535 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/pubSales.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.535 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_reviewrating.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.535 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_booksHolder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.536 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/manageTabs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.536 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/bookdtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.536 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_discforum.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.536 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_slider.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.537 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/independentContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.537 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/quizcreatorbulkinput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.537 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_buyChapters.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.538 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/quizcreatorbulk.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.538 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_booksHolderTemplate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.538 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/videoplayer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.538 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/studySet.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.539 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/bookCreateNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.539 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/arihant.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.539 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/errorPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.539 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_wonderGoaStudySet.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.540 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_videoPlay.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.540 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/videoExplanation.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.540 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_readerView.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.541 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_qaSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.541 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/manageExams.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.541 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_readingSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.541 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/deleteAccount.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.542 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_bookGPTTab.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.542 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/qandacreator.js] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.544 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/layouts/main.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.544 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/sales.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.544 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/profile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.545 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/contentCreators.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.545 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.545 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/reports.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.546 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/users.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.546 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/classes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.546 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/students.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.546 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/content.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.547 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/teachers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.547 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/amazonLink.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.547 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/test.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.548 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/fixBrokenResources.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.548 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/progress/progressReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.549 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/libraryBooks/myLibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.549 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/libraryBooks/_publishersList.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.549 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.550 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/help.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.550 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.550 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/privacy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.551 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.551 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/_storeHolder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.551 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/_commonfooter_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.551 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/contact.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.552 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.552 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.552 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.553 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/packages.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.553 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/feedback.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.553 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/termsCondition.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.554 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/library.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.554 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/terms.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.554 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/default/discussionBoard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.555 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/default/chapterDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.555 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/default/jsonPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.555 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/default/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.556 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/default/remote.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.556 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/default/_showResult.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.556 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/myTracker.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.557 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.557 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/specimenCopyRequests.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.557 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/orders.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.558 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/favouriteMcqs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.558 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/teachersNomineeForm.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.558 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/teachersPollResult.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.558 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/addUserAndBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.559 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/_ordersListInclude.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.559 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/editprofile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.559 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/nominations.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.560 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/nomineeDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.560 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/publisherManagement/addPublisher.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.560 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/publisherManagement/publisherReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.561 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/publisherManagement/publisher.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.561 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/externalReportInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.561 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/reports.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.562 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/instituteReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.562 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.562 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/pubSales.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.562 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/getBulkUsersAddedReportInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.563 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/scratchCardReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.563 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/mergeQuizzes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.564 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/mcqSorter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.564 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/addNotes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.564 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/sectionModifier.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.564 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/aiBookMigrate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.565 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/findParentBook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.565 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/mergeMCQInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.566 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/addBulkUsersAndBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.566 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/formulaFixer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.566 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/bookInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.566 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/fileUploader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.567 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/_fileUploaderInclude.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.567 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/imageFixer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.567 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/quizExtractor.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.567 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/uploadMCQ.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.568 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/extractPDF.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.568 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/upload.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.568 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/htmlClassExtractor.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.569 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/wordUpload.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.569 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/exportmcqspage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.569 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/uploadQA.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.570 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_instructorResourcesContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.570 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/faq.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.570 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/studentResourcesLocked.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.571 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_userlogin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.571 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.571 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/ebook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.571 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/privacy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.572 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.572 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/instructorResources.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.572 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/instructorResourcesAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.573 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_studentResourcesLocked.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.573 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/instructorResourcesLocked.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.573 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/aboutus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.573 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.574 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/register.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.574 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_askAuthorModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.574 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/verification.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.575 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/disciplines.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.575 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/doris.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.575 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_additionalStudentInfo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.575 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/terms.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.576 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsLibrary/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.576 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsLibrary/myLibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.577 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsLibrary/accessCode.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.577 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/document/create.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.578 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/document/list.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.578 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/document/excelUpload.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.578 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/faq.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.578 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.579 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/ebouquetReviewerLogin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.579 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/virtualLibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.579 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.579 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/_storeHolder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.580 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/_commonfooter_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.580 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/information.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.580 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/contact.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.580 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/_accessCodeLogin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.580 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.581 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/howItWorks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.581 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/termsOfUse.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.581 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.581 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.581 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/privacyPolicy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.582 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/decision.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.582 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/requestDemo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.582 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/cookies.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.583 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/printOrderManagement/orderDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.583 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mocktests/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.583 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mocktests/_relatedBooksAndLeaderboard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.584 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mocktests/listExams.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.584 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mocktests/_topLevelTests.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.584 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mocktests/examPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.584 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mocktests/_mockTestFaq.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.585 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/articles/showGradeBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.585 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/articles/getGradeBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.585 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/articles/showBlogPages.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.585 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/affiliationAdmin/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.586 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/affiliationAdmin/categoryManager.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.586 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/affiliationAdmin/searchResults.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.586 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/list.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.587 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/bookgpt.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.587 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_bookgptChatViewer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.587 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_bookgptPDFViewer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.587 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/showGptLogsForResId.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.588 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/showGptLogsForFeedback.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.588 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/showGptLogsForUser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.588 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/manualGpt.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.593 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_bookgptPDFViewerMobile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.593 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_bookgptChatViewerMobile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.594 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/myDriveReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.594 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_driveChat.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.594 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_driveMobileReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.595 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/pdfReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.595 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_driveMobileChat.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.595 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_driveReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.595 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_chatModule.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.596 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_codeRunner.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.596 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_bookgptContentHolder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.597 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/bookmark/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.597 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/digitalLibrary/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.597 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/digitalLibrary/showCategory.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.597 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/digitalLibrary/_categorySection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.598 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/digitalLibrary/adminIndex.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.598 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.599 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/promptTemplateManager.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.599 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/duplicateFixer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.599 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/kindleTemplate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.599 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/autogptTask.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.600 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/test.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.600 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/autoGPTAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.601 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/finance/salesReportByPublisher.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.601 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/finance/salesReportForAccounts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.601 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/partner/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.602 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/studyMaterial/_copilot.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.602 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/studyMaterial/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.602 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/webInteraction/manageLinks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.602 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/webInteraction/_aira.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.603 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/webInteraction/pageInteractions.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.603 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/howItWorks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.604 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.604 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.604 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/contact.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.605 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/faq.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.605 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.605 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/benefits.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.605 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/features.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.606 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/printBookBundling.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.606 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sdk/sdkIntegrationReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.607 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/listBatches.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.607 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/listUsersInBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.607 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/addInstitute.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.608 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/editUser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.608 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/getBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.608 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/adminDashboard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.608 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/listInstitutes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.609 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/listBooksInBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.609 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/assignUserToBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.609 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/assignBookToBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.609 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/editBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.610 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/editCourse.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.610 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/createBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.610 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/listCourses.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.611 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/createCourse.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.611 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/manageInstitutePrompts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.611 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/promptLanguages/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.612 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/onlineTest/testQuestionAnalytics.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.612 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/onlineTest/blockStudents.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.612 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/onlineTest/listTests.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.613 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/onlineTest/testResults.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.613 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/onlineTest/viewQuestions.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.614 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExtractor/mcqExtractor.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.614 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExtractor/_api_modal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.614 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExtractor/createSolution.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.615 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExtractor/mcqTranslator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.615 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/contentdelivery/getPdfFile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.615 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/contentdelivery/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.616 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/contentdelivery/cookieTest.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.616 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/createQuestionPaperPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.617 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/listQuestionPapers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.617 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/viewQuestionPaper.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.617 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/addQuestionType.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.617 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/viewPattern.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.618 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/listPatterns.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.618 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/addSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.618 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/printQuestionPaper.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.619 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/aireport/dashboard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.619 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionTypes/create.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.619 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionTypes/list.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.620 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/contentCreation/getSolvedPaperList.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.620 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/chat/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.620 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/chat/_rightContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.621 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/chat/_leftContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.621 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExporter/sample.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.621 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExporter/chapterPdf.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.622 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExporter/bookTitle.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.622 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExporter/theoryChapter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.622 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExporter/_theoryChapterContents.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.623 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/theoryBooks/theoryBookInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.623 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/liveMockTests/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.623 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/liveMockTests/manageLiveMockTests.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.624 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderslate/LoginFilters.groovy] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.624 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/spring/resources.groovy] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:00.633 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp/views.properties] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
08-Sep-2025 12:36:11.291 INFO [ws-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
08-Sep-2025 12:36:13.658 INFO [ws-startStop-1] org.apache.catalina.core.ApplicationContext.log 2 Spring WebApplicationInitializers detected on classpath

Configuring Spring Security Core ...
... finished configuring Spring Security Core


Configuring Spring Security REST 2.0.0.M2...
... finished configuring Spring Security REST

	... with GORM support
08-Sep-2025 12:38:18.990 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployDescriptor Deployment of configuration descriptor /u01/apache-tomcat-8.5.13/conf/Catalina/ws/ROOT.xml has finished in 153,328 ms
08-Sep-2025 12:38:18.993 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive /u01/apache-tomcat-8.5.13/webapps/wonderlive.war
08-Sep-2025 12:38:22.647 INFO [ws-startStop-1] org.apache.catalina.core.ApplicationContext.log 2 Spring WebApplicationInitializers detected on classpath

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::        (v2.2.2.RELEASE)

2025-09-08 12:38:23.952  INFO 156863 --- [ ws-startStop-1] com.wonderslate.chat.ServletInitializer  : Starting ServletInitializer v0.0.1-SNAPSHOT on ip-172-31-30-27.us-west-2.compute.internal with PID 156863 (/u01/apache-tomcat-8.5.13/webapps/wonderlive/WEB-INF/classes started by root in /u01/apache-tomcat-8.5.13/bin)
2025-09-08 12:38:23.965  INFO 156863 --- [ ws-startStop-1] com.wonderslate.chat.ServletInitializer  : No active profile set, falling back to default profiles: default
08-Sep-2025 12:38:25.637 INFO [ws-startStop-1] org.apache.catalina.core.ApplicationContext.log Initializing Spring embedded WebApplicationContext
2025-09-08 12:38:25.638  INFO 156863 --- [ ws-startStop-1] o.s.web.context.ContextLoader            : Root WebApplicationContext: initialization completed in 1555 ms
2025-09-08 12:38:26.134  INFO 156863 --- [ ws-startStop-1] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'clientInboundChannelExecutor'
2025-09-08 12:38:26.140  INFO 156863 --- [ ws-startStop-1] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'clientOutboundChannelExecutor'
2025-09-08 12:38:26.193  INFO 156863 --- [ ws-startStop-1] o.s.s.c.ThreadPoolTaskScheduler          : Initializing ExecutorService 'messageBrokerTaskScheduler'
2025-09-08 12:38:26.266  INFO 156863 --- [ ws-startStop-1] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'brokerChannelExecutor'
2025-09-08 12:38:27.235  WARN 156863 --- [ ws-startStop-1] o.s.b.a.f.FreeMarkerAutoConfiguration    : Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-09-08 12:38:27.498  INFO 156863 --- [ ws-startStop-1] o.s.m.s.b.SimpleBrokerMessageHandler     : Starting...
2025-09-08 12:38:27.499  INFO 156863 --- [ ws-startStop-1] o.s.m.s.b.SimpleBrokerMessageHandler     : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [DefaultSubscriptionRegistry[cache[0 destination(s)], registry[0 sessions]]]]
2025-09-08 12:38:27.500  INFO 156863 --- [ ws-startStop-1] o.s.m.s.b.SimpleBrokerMessageHandler     : Started.
2025-09-08 12:38:27.524  INFO 156863 --- [ ws-startStop-1] com.wonderslate.chat.ServletInitializer  : Started ServletInitializer in 4.6 seconds (JVM running for 163.774)
08-Sep-2025 12:38:27.544 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive /u01/apache-tomcat-8.5.13/webapps/wonderlive.war has finished in 8,551 ms
08-Sep-2025 12:38:27.545 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory /u01/apache-tomcat-8.5.13/webapps/.well-known
08-Sep-2025 12:38:27.573 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory /u01/apache-tomcat-8.5.13/webapps/.well-known has finished in 27 ms
08-Sep-2025 12:38:27.573 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory /u01/apache-tomcat-8.5.13/webapps/tools
08-Sep-2025 12:38:27.600 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory /u01/apache-tomcat-8.5.13/webapps/tools has finished in 27 ms
08-Sep-2025 12:38:27.620 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["ajp-nio-8009"]
08-Sep-2025 12:38:27.638 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in 162038 ms
is this compiled or not
08-Sep-2025 12:38:47.742 INFO [ajp-nio-8009-exec-7] org.apache.tomcat.util.http.parser.Cookie.logInvalidHeader A cookie header was received [Sep 08 2025 18:08:47 GMT+0530 (India Standard Time); pomodoroCurrentDuration=0; daysDuration=0] that contained an invalid cookie. That cookie will be ignored.Note: further occurrences of this error will be logged at DEBUG level.
2025-09-08 12:39:26.265  INFO 156863 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats    : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
bookId in the beginning216805
queryBookId in the afterwards 216805
sql= select id,book_type bookType,list_price  listPrice,sell_price sellPrice,currency_cd currencyCd,free_chat_tokens freeChatTokens FROM wsshop.book_price_dtl  where book_id=216805 ORDER BY FIELD(bookType, 'printbook', 'eBook','testSeries', 'combo','upgrade','bookGPT')
result id=1313 bookType=eBook listPrice=99.0 sellPrice=49.0 currencyCd=INR freeChatTokens=null
chapter: [topicTitle:Physics and Measurement, subtopics:[[title:Units and Systems of Measurement, learningObjective:Understand different units of measurement and the SI system including fundamental and derived units., keyConcepts:[Units of measurement, System of units, SI units, Fundamental units, Derived units], bloomLevel:Remember, prerequisites:[], applications:[Standardization in scientific experiments, Engineering measurements], hasFormulas:false], [title:Least Count and Significant Figures, learningObjective:Learn how to determine least count and use significant figures to express measurement precision., keyConcepts:[Least count, Significant figures, Precision, Accuracy], bloomLevel:Understand, prerequisites:[Basic arithmetic], applications:[Instrument calibration, Error minimization in measurements], hasFormulas:false], [title:Errors in Measurements, learningObjective:Identify types of measurement errors and understand their impact on experimental results., keyConcepts:[Systematic errors, Random errors, Absolute error, Relative error, Percentage error], bloomLevel:Understand, prerequisites:[Least count, Significant figures], applications:[Improving experimental accuracy, Quality control in manufacturing], hasFormulas:true], [title:Dimensions and Dimensional Analysis, learningObjective:Use dimensional analysis to check the correctness of physical equations and derive relations between physical quantities., keyConcepts:[Dimensions of physical quantities, Dimensional formula, Dimensional equation, Applications of dimensional analysis], bloomLevel:Apply, prerequisites:[Units and systems of measurement], applications:[Deriving formulas, Checking equation consistency, Unit conversion], hasFormulas:true]]]
chapter: [topicTitle:Kinematics, subtopics:[[title:Frame of Reference and Motion in a Straight Line, learningObjective:Understand the concept of frame of reference and describe motion along a straight line., keyConcepts:[Frame of reference, Position, Displacement, Distance, Motion in a straight line], bloomLevel:Remember, prerequisites:[Basic coordinate system], applications:[Vehicle tracking, Motion analysis in sports], hasFormulas:false], [title:Speed and Velocity, learningObjective:Distinguish between speed and velocity and calculate average and instantaneous values., keyConcepts:[Speed, Velocity, Average speed, Instantaneous velocity, Uniform and non-uniform motion], bloomLevel:Understand, prerequisites:[Frame of reference, Motion in a straight line], applications:[Traffic flow analysis, Navigation systems], hasFormulas:true], [title:Uniformly Accelerated Motion and Graphs, learningObjective:Analyze motion with constant acceleration using velocity-time and position-time graphs and equations., keyConcepts:[Uniform acceleration, Velocity-time graph, Position-time graph, Equations of motion], bloomLevel:Apply, prerequisites:[Speed and velocity], applications:[Vehicle acceleration, Free fall analysis], hasFormulas:true], [title:Scalars and Vectors, learningObjective:Understand scalar and vector quantities and perform vector addition, subtraction, and multiplication., keyConcepts:[Scalars, Vectors, Vector addition, Vector subtraction, Scalar product, Vector product, Unit vector, Resolution of vectors], bloomLevel:Understand, prerequisites:[Basic geometry], applications:[Force analysis, Navigation, Engineering mechanics], hasFormulas:true], [title:Relative Velocity, learningObjective:Calculate relative velocity of objects moving in one and two dimensions., keyConcepts:[Relative velocity, Reference frames, Vector addition], bloomLevel:Apply, prerequisites:[Vectors, Frame of reference], applications:[Airplane navigation, River boat problems], hasFormulas:true], [title:Motion in a Plane, learningObjective:Analyze two-dimensional motion including projectile and uniform circular motion., keyConcepts:[Projectile motion, Uniform circular motion, Trajectory, Centripetal acceleration, Centripetal force], bloomLevel:Apply, prerequisites:[Vectors, Uniformly accelerated motion], applications:[Sports ballistics, Satellite orbits, Vehicle turning], hasFormulas:true]]]
chapter: [topicTitle:Laws of Motion, subtopics:[[title:Newton’s First Law and Force, learningObjective:Understand the concept of force and inertia and Newton’s First Law of motion., keyConcepts:[Force, Inertia, Newton’s First Law], bloomLevel:Remember, prerequisites:[Scalars and vectors], applications:[Safety mechanisms, Vehicle design], hasFormulas:false], [title:Newton’s Second Law and Momentum, learningObjective:Apply Newton’s Second Law to relate force, mass, and acceleration and understand momentum and impulse., keyConcepts:[Newton’s Second Law, Force = mass × acceleration, Momentum, Impulse], bloomLevel:Apply, prerequisites:[Newton’s First Law, Vectors], applications:[Crash analysis, Rocket propulsion], hasFormulas:true], [title:Newton’s Third Law and Conservation of Momentum, learningObjective:Explain action-reaction forces and apply conservation of linear momentum in collisions., keyConcepts:[Newton’s Third Law, Action and reaction, Conservation of momentum, Elastic and inelastic collisions], bloomLevel:Apply, prerequisites:[Newton’s Second Law, Momentum], applications:[Collision safety, Ballistics, Sports physics], hasFormulas:true], [title:Equilibrium and Friction, learningObjective:Analyze conditions for equilibrium of concurrent forces and understand static, kinetic, and rolling friction., keyConcepts:[Equilibrium, Concurrent forces, Static friction, Kinetic friction, Rolling friction, Laws of friction], bloomLevel:Analyze, prerequisites:[Newton’s Laws, Vectors], applications:[Structural engineering, Vehicle braking, Machinery design], hasFormulas:true], [title:Dynamics of Uniform Circular Motion, learningObjective:Understand centripetal force and analyze motion of vehicles on level and banked roads., keyConcepts:[Uniform circular motion, Centripetal force, Banked road, Vehicle dynamics], bloomLevel:Apply, prerequisites:[Motion in a plane, Newton’s Laws], applications:[Road design, Amusement park rides, Satellite motion], hasFormulas:true]]]
chapter: [topicTitle:Work, Energy, and Power, subtopics:[[title:Work Done by Forces, learningObjective:Calculate work done by constant and variable forces., keyConcepts:[Work done, Work by constant force, Work by variable force], bloomLevel:Apply, prerequisites:[Force, Displacement], applications:[Mechanical work calculation, Energy transfer in machines], hasFormulas:true], [title:Kinetic and Potential Energy, learningObjective:Understand kinetic and potential energy and their mathematical expressions., keyConcepts:[Kinetic energy, Potential energy, Energy formulas], bloomLevel:Remember, prerequisites:[Work done], applications:[Energy conservation, Mechanical systems], hasFormulas:true], [title:Work-Energy Theorem and Power, learningObjective:Apply the work-energy theorem and define power as the rate of doing work., keyConcepts:[Work-energy theorem, Power, Rate of work], bloomLevel:Apply, prerequisites:[Work done, Energy], applications:[Engine performance, Electrical power calculations], hasFormulas:true], [title:Potential Energy of a Spring and Conservation of Mechanical Energy, learningObjective:Calculate potential energy stored in a spring and apply conservation of mechanical energy., keyConcepts:[Potential energy of spring, Hooke’s law, Conservation of mechanical energy, Conservative and non-conservative forces], bloomLevel:Apply, prerequisites:[Potential energy, Work done], applications:[Spring mechanics, Pendulum motion, Energy efficiency], hasFormulas:true], [title:Motion in a Vertical Circle, learningObjective:Analyze forces and energy changes in objects moving in a vertical circular path., keyConcepts:[Vertical circular motion, Tension, Potential and kinetic energy variation], bloomLevel:Analyze, prerequisites:[Uniform circular motion, Energy concepts], applications:[Roller coasters, Pendulum swings], hasFormulas:true], [title:Elastic and Inelastic Collisions, learningObjective:Differentiate between elastic and inelastic collisions and solve problems in one and two dimensions., keyConcepts:[Elastic collisions, Inelastic collisions, Momentum conservation, Energy conservation in collisions], bloomLevel:Apply, prerequisites:[Conservation of momentum, Energy], applications:[Vehicle crash analysis, Particle physics, Sports collisions], hasFormulas:true]]]
bookId in the beginning312870
queryBookId in the afterwards 312870
siteName=prepjoy
is this compiled or not
bookId in the beginning216554
queryBookId in the afterwards 216554
cs
coming here man for ogin
SELECT id, res_id, username FROM quiz_rec_mst WHERE username = 71_8754178781 AND res_id IS NOT NULL
2025-09-08 13:06:22.904 ERROR --- [io-8009-exec-12] g.a.s.c.w.data.LiveMockTestsService      : Error accessing QuizRecMst: Unknown column '71_8754178781' in 'where clause'

java.sql.SQLSyntaxErrorException: Unknown column '71_8754178781' in 'where clause'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	at sun.reflect.GeneratedMethodAccessor439.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.tomcat.jdbc.pool.StatementFacade$StatementProxy.invoke(StatementFacade.java:138)
	at com.sun.proxy.$Proxy89.executeQuery(Unknown Source)
	at groovy.sql.Sql$QueryCommand.runQuery(Sql.java:4684)
	at groovy.sql.Sql$AbstractQueryCommand.execute(Sql.java:4598)
	at groovy.sql.Sql.rows(Sql.java:1715)
	at groovy.sql.Sql.rows(Sql.java:1633)
	at groovy.sql.Sql$rows.call(Unknown Source)
	at com.wonderslate.sqlutil.SafeSql.rows(SafeSql.groovy:24)
	at com.wonderslate.sqlutil.SafeSql$rows.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:48)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:113)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at com.wonderslate.data.LiveMockTestsService.$tt__getCompletedMockTests(LiveMockTestsService.groovy:236)
	at com.wonderslate.data.LiveMockTestsService$_getCompletedMockTests_closure3.doCall(LiveMockTestsService.groovy)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:93)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:325)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:294)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1027)
	at groovy.lang.Closure.call(Closure.java:414)
	at groovy.lang.Closure.call(Closure.java:430)
	at grails.transaction.GrailsTransactionTemplate$2.doInTransaction(GrailsTransactionTemplate.groovy:96)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:133)
	at grails.transaction.GrailsTransactionTemplate.execute(GrailsTransactionTemplate.groovy:93)
	at com.wonderslate.data.LiveMockTestsService.getCompletedMockTests(LiveMockTestsService.groovy)
	at com.wonderslate.data.LiveMockTestsService$getCompletedMockTests$1.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:48)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:113)
	at com.wonderslate.data.LiveMockTestsController.$tt__getCompletedMockTests(LiveMockTestsController.groovy:173)
	at com.wonderslate.data.LiveMockTestsController$_getCompletedMockTests_closure2.doCall(LiveMockTestsController.groovy)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:93)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:325)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:294)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1027)
	at groovy.lang.Closure.call(Closure.java:414)
	at groovy.lang.Closure.call(Closure.java:430)
	at grails.transaction.GrailsTransactionTemplate$2.doInTransaction(GrailsTransactionTemplate.groovy:96)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:133)
	at grails.transaction.GrailsTransactionTemplate.execute(GrailsTransactionTemplate.groovy:93)
	at com.wonderslate.data.LiveMockTestsController.getCompletedMockTests(LiveMockTestsController.groovy)
	at org.grails.core.DefaultGrailsControllerClass$MethodHandleInvoker.invoke(DefaultGrailsControllerClass.java:223)
	at org.grails.core.DefaultGrailsControllerClass.invoke(DefaultGrailsControllerClass.java:188)
	at org.grails.web.mapping.mvc.UrlMappingsInfoHandlerAdapter.handle(UrlMappingsInfoHandlerAdapter.groovy:90)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:963)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:897)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:970)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:861)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:635)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:846)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:230)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at javax.servlet.FilterChain$doFilter.call(Unknown Source)
	at grails.plugin.springsecurity.rest.RestLogoutFilter.doFilter(RestLogoutFilter.groovy:80)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.boot.web.filter.ApplicationContextHeaderFilter.doFilterInternal(ApplicationContextHeaderFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.boot.actuate.trace.WebRequestTraceFilter.doFilterInternal(WebRequestTraceFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:317)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:127)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:91)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at grails.plugin.springsecurity.web.UpdateRequestContextHolderExceptionTranslationFilter.doFilter(UpdateRequestContextHolderExceptionTranslationFilter.groovy:64)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at javax.servlet.FilterChain$doFilter.call(Unknown Source)
	at grails.plugin.springsecurity.rest.RestTokenValidationFilter.processFilterChain(RestTokenValidationFilter.groovy:122)
	at sun.reflect.GeneratedMethodAccessor480.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrapNoCoerce.invoke(PogoMetaMethodSite.java:210)
	at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.callCurrent(PogoMetaMethodSite.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:190)
	at grails.plugin.springsecurity.rest.RestTokenValidationFilter.doFilter(RestTokenValidationFilter.groovy:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.web.filter.GrailsAnonymousAuthenticationFilter.doFilter(GrailsAnonymousAuthenticationFilter.groovy:53)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.authentication.rememberme.RememberMeAuthenticationFilter.doFilter(RememberMeAuthenticationFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:169)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at javax.servlet.FilterChain$doFilter.call(Unknown Source)
	at grails.plugin.springsecurity.rest.RestAuthenticationFilter.doFilter(RestAuthenticationFilter.groovy:143)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.web.authentication.logout.MutableLogoutFilter.doFilter(MutableLogoutFilter.groovy:62)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.web.SecurityRequestHolderFilter.doFilter(SecurityRequestHolderFilter.groovy:58)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:214)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:177)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.grails.web.servlet.mvc.GrailsWebRequestFilter.doFilterInternal(GrailsWebRequestFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.grails.web.filters.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:67)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:197)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.boot.actuate.autoconfigure.MetricsFilter.doFilterInternal(MetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.boot.web.support.ErrorPageFilter.doFilter(ErrorPageFilter.java:117)
	at org.springframework.boot.web.support.ErrorPageFilter.access$000(ErrorPageFilter.java:61)
	at org.springframework.boot.web.support.ErrorPageFilter$1.doFilterInternal(ErrorPageFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.boot.web.support.ErrorPageFilter.doFilter(ErrorPageFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:198)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:478)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:80)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:341)
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:486)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:861)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1455)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

