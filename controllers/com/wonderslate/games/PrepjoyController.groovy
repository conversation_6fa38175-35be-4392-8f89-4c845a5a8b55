package com.wonderslate.games

import com.wonderslate.DataNotificationService
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.KeyValueMst
import com.wonderslate.data.PrepjoyService
import com.wonderslate.data.ResourceDtl
import com.wonderslate.data.SiteDtl
import com.wonderslate.data.SiteManagerService
import com.wonderslate.data.SiteMst
import com.wonderslate.data.SitemapService
import com.wonderslate.data.UtilService
import com.wonderslate.groups.GroupsService
import com.wonderslate.prepjoy.DailyTestsDtl
import com.wonderslate.prepjoy.DailyTestsMst
import com.wonderslate.prepjoy.QuizRanks
import com.wonderslate.prepjoy.QuizRecMst
import com.wonderslate.prepjoy.UserPointsPrepJoy
import com.wonderslate.publish.Publishers
import com.wonderslate.shop.WsshopService
import com.wonderslate.usermanagement.AccessCodeService
import com.wonderslate.usermanagement.PlayAccessCodes
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserChallengesMst
import com.wonderslate.usermanagement.UserManagementService
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import org.apache.commons.lang.StringUtils
import org.grails.web.util.WebUtils
import org.springframework.security.core.context.SecurityContextHolder

import javax.servlet.http.Cookie
import java.text.SimpleDateFormat

class PrepjoyController {

    PrepjoyService prepjoyService
    def redisService
    def springSecurityService
    def rememberMeServices
    DataProviderService dataProviderService
    UtilService utilService
    DataNotificationService dataNotificationService
    AccessCodeService accessCodeService
    UserManagementService userManagementService
    WsshopService wsshopService
    SiteManagerService siteManagerService
    SitemapService sitemapService
    GroupsService groupsService

    @Transactional
    def index() {

        redirect([uri: '/prepjoy/eBooks'])
    }

    def getNameAndPlace(){
        def json = [challengerName: prepjoyService.getName(), challengerPlace: prepjoyService.getPlace()]
        render json as JSON
    }

    def currentAffairsDaily(){
        ['title':'Current Affairs - Wonderslate']

    }

    @Transactional

    def createPrepjoyUsers(){
       prepjoyService.createPrepjoyUsers(new Integer(params.siteId))
        render "success"
    }

    @Transactional
    def setFirstTenPlays(){
        prepjoyService.setFirstTenPlays(new Integer(params.siteId))
        render "success"
    }

    @Transactional
    def quizSubmit(){
        def siteId = ""+session['siteId']
        def jsonObject = request.JSON
        if("true".equals(jsonObject.isFromExternal) && springSecurityService.currentUser==null){
            String usernameTemp = "<EMAIL>"
            User user = User.findByUsername(usernameTemp)
            session['userdetails'] = user
            springSecurityService.reauthenticate(user.username, user.password)
            def authentication = SecurityContextHolder.context.authentication
            rememberMeServices.loginSuccess(request, response, authentication)
        }
        HashMap returnValues = prepjoyService.updateQuizResults(request)
        if(springSecurityService.currentUser!=null) {
            if (redisService.("userPrepjoydetails_" + springSecurityService.currentUser.username) == null) prepjoyService.getUserPrepJoyDetails(springSecurityService.currentUser.username)
        }
        def ranks=[]

        if("71".equals(siteId)) ranks = prepjoyService.getQuizRanks(""+request.JSON.resId)
        def json = ['status':'success', 'newMedal':returnValues.get("newMedal"),'totalPoints':returnValues.get("totalPoints"),
                    'newBadge':returnValues.get("newBadge"),challengerName: prepjoyService.getName(), challengerPlace: prepjoyService.getPlace(),
                     prepJoyUserDetails:springSecurityService.currentUser!=null?redisService.("userPrepjoydetails_"+springSecurityService.currentUser.username):null,'quizRecId':returnValues.get("quizRecId"),
                     level:returnValues.get("level"),syllabus:returnValues.get("syllabus"),grade:returnValues.get("grade"),subject:returnValues.get("subject"),
                    newRanks:returnValues.get("newRanks"),newWeeklyRanks:returnValues.get("newWeeklyRanks"),newMonthlyRanks:returnValues.get("newMonthlyRanks"),
                    newRanksInstitute:returnValues.get("newRanksInstitute"),newWeeklyRanksInstitute:returnValues.get("newWeeklyRanksInstitute"),
                    newMonthlyRanksInstitute:returnValues.get("newMonthlyRanksInstitute"),ranks: ranks,quizStatisticsList:returnValues.get("quizStatisticsList"),testGenId:returnValues.get("testGenId"), showTestResults: returnValues.get("showResults"), testResultDate: returnValues.get("testResultDate"), quizName: returnValues.get("quizName")]
        render json as JSON
    }

    @Transactional @Secured(['ROLE_USER'])
    def getUserPrepJoyDetails() {
        if(redisService.("userPrepjoydetails_"+springSecurityService.currentUser.username)==null) prepjoyService.getUserPrepJoyDetails(springSecurityService.currentUser.username)
        if(redisService.("userDefaultTimeLog_"+springSecurityService.currentUser.username)==null) userManagementService.getUserDefaultTimeLog(springSecurityService.currentUser.username)
        if(redisService.("userQuizDefaultTimeLog_"+springSecurityService.currentUser.username)==null) userManagementService.getUserQuizDefaultTimeLog(springSecurityService.currentUser.username)

        def json =['details':redisService.("userPrepjoydetails_"+springSecurityService.currentUser.username),
        'defaultSevenDayTime':redisService.("userDefaultTimeLog_"+springSecurityService.currentUser.username),
        'defaultSevenDayQuizTime':redisService.("userQuizDefaultTimeLog_"+springSecurityService.currentUser.username)]
        render json as JSON
    }

    @Transactional
    def getPrepjoyDailyRanks() {
        String rankDate = params.rankDate
        Integer siteId = utilService.getSiteId(request,session)
       if(redisService.("prepJoyTodaysRank_"+siteId+"_" + rankDate)==null) prepjoyService.getDailyLeaderBoard(rankDate,siteId,null)
        render redisService.("prepJoyTodaysRank_"+siteId+"_" + rankDate)
    }
    @Transactional
    def getPrepjoyWeeklyRanks() {
        String rankDate = params.rankDate
        Integer siteId = utilService.getSiteId(request,session)
        if(redisService.("prepJoyWeeklyRank_"+siteId+"_" + rankDate)==null) prepjoyService.getWeeklyLeaderBoard(rankDate,siteId,null)
        render redisService.("prepJoyWeeklyRank_"+siteId+"_" + rankDate)
    }
    @Transactional
    def getPrepjoyMonthlyRanks() {
        String rankDate = params.rankDate
        Integer siteId = utilService.getSiteId(request,session)
       if(redisService.("prepJoyMonthlyRank_"+siteId+"_" + rankDate)==null) prepjoyService.getMonthlyLeaderBoard(rankDate,siteId,null)
        render redisService.("prepJoyMonthlyRank_"+siteId+"_" + rankDate)
    }

    @Transactional
    def getPrepjoyDailyRanksInstitute() {

        String rankDate = params.rankDate
        Integer siteId = utilService.getSiteId(request,session)
        String instituteId = params.instituteId
        String institutePrefix="institute_"+instituteId+"_"

        if(redisService.("prepJoyTodaysRank_"+siteId+"_" + rankDate)==null) prepjoyService.getDailyLeaderBoard(rankDate,siteId,null)
        if(StringUtils.isNumeric(instituteId)&&redisService.(institutePrefix+"prepJoyTodaysRank_"+siteId+"_" + rankDate)==null) prepjoyService.getDailyLeaderBoard(rankDate,siteId,instituteId)

        def json = [mainranks:redisService.("prepJoyTodaysRank_"+siteId+"_" + rankDate),
                    institutionranks:redisService.(institutePrefix+"prepJoyTodaysRank_"+siteId+"_" + rankDate)]
        render json as JSON
    }
    @Transactional
    def getPrepjoyWeeklyRanksInstitute() {
        String rankDate = params.rankDate
        Integer siteId = utilService.getSiteId(request,session)
        String instituteId = params.instituteId
        String institutePrefix="institute_"+instituteId+"_"

        if(redisService.("prepJoyWeeklyRank_"+siteId+"_" + rankDate)==null) prepjoyService.getWeeklyLeaderBoard(rankDate,siteId,null)
        if(StringUtils.isNumeric(instituteId)&&redisService.(institutePrefix+"prepJoyWeeklyRank_"+siteId+"_" + rankDate)==null) prepjoyService.getWeeklyLeaderBoard(rankDate,siteId,instituteId)

        def json = [mainranks:redisService.("prepJoyWeeklyRank_"+siteId+"_" + rankDate),
                    institutionranks:redisService.(institutePrefix+"prepJoyWeeklyRank_"+siteId+"_" + rankDate)]
        render json as JSON

    }
    @Transactional
    def getPrepjoyMonthlyRanksInstitute() {
        String rankDate = params.rankDate
        Integer siteId = utilService.getSiteId(request,session)
        String instituteId = params.instituteId
        String institutePrefix="institute_"+instituteId+"_"

        if(redisService.("prepJoyMonthlyRank_"+siteId+"_" + rankDate)==null) prepjoyService.getMonthlyLeaderBoard(rankDate,siteId,null)
        if(StringUtils.isNumeric(instituteId)&&redisService.(institutePrefix+"prepJoyMonthlyRank_"+siteId+"_" + rankDate)==null) prepjoyService.getMonthlyLeaderBoard(rankDate,siteId,instituteId)

        def json = [mainranks:redisService.("prepJoyMonthlyRank_"+siteId+"_" + rankDate),
                    institutionranks:redisService.(institutePrefix+"prepJoyMonthlyRank_"+siteId+"_" + rankDate)]
        render json as JSON
    }
    @Transactional
    def prepJoyGame() {
        String title = "MCQ Test"
        String seoDesc = "MCQ Test"
        boolean shuffle = false
        boolean enableai = false
        String bookId = ""
        if(params.dailyTestId!=null){
            DailyTestsMst dailyTestsMst = prepjoyService.getDailyTestMst(params.dailyTestId)
            title = dailyTestsMst.testName+" mocktest for "+params.dateInput

        }else if("caDaily".equals(params.mode)){
            ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Integer(params.resId))
            title = resourceDtl.resourceName+" MCQs"
        }else if("caWeekly".equals(params.mode)){
            title = "Weekly current affairs MCQs for "+params.dateInput
        }else if("caMontly".equals(params.mode)){
            title = "Monthly current affairs MCQs for "+params.dateInput
        }else if(params.resId!=null){
            ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Integer(params.resId))
            title = resourceDtl.resourceName+" MCQs"
            if(resourceDtl.chapterId!=null){
                ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
                BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
                bookId = booksMst.id
                title = chaptersMst.name+" - "+resourceDtl.resourceName
                if("bookgpt".equals(booksMst.bookType) || "ebookwithai".equals(booksMst.bookType)){
                    enableai = true
                }
            }
        }
        seoDesc = title
        [showLibrary:utilService.hasLibraryAccess(request,utilService.getSiteId(request,session)),
        'title': title,commonTemplate:"true",seoDesc: seoDesc,shuffle:shuffle, enableai: enableai, bookId:bookId]
    }
    @Transactional @Secured(['ROLE_USER'])
    def webMcq() {

    }

    @Transactional
    def getMultiDaysQuiz(){
        String dateInput = params.dateInput
        String noOfQuestions = params.noOfQuestions
        String noOfDays = params.noOfDays
        String currentAffairsType = "Main"
        if(params.currentAffairsType!=null) currentAffairsType = params.currentAffairsType

        if(redisService.("multiDaysCurrentaffairs_"+dateInput+"_"+noOfQuestions+"_"+noOfDays+"_"+currentAffairsType)==null) prepjoyService.getMultiDaysQuiz(dateInput,noOfQuestions,noOfDays,currentAffairsType)


        def  jsonAnswers = redisService.("multiDaysCurrentaffairs_"+dateInput+"_"+noOfQuestions+"_"+noOfDays+"_"+currentAffairsType)

        def json =
                [
                        'results'     : jsonAnswers,
                        'status'      : jsonAnswers ? "OK" : "Nothing present",
                        'language1'   : "Hindi" ,
                        'language2'   : "English",
                        'challengerName' : prepjoyService.getName(),
                        'challengerPlace' : prepjoyService.getPlace()

                ]

        render json as JSON
    }

    @Transactional
    def getWeeklyCurrentAffairsReadingMaterials(){
        String dateInput = params.dateInput
        String currentAffairsType = "Main"

        if(params.currentAffairsType!=null) currentAffairsType = params.currentAffairsType
        if(redisService.("weeklyCurrentAffairsRead_"+dateInput+"_"+currentAffairsType)==null) prepjoyService.getWeeklyCurrentAffairsReadingMaterials(dateInput,currentAffairsType)

        def json = ['readingMaterials':redisService.("weeklyCurrentAffairsRead_"+dateInput+"_"+currentAffairsType)]
        render json as JSON
    }

    @Transactional
    def createDailyTests(){
        List tests = DailyTestsMst.findAllByStatus("Active")
        tests.each {test ->
              prepjoyService.createDailyTests(test.id)

        }
        render "completed"
    }

    @Transactional

    def getDailyTestsLatestAndStartDates(){
        String dailyTestId = params.dailyTestId
        if(redisService.("dailyTestsLatestDate"+"_"+dailyTestId)==null) prepjoyService.getDailyTestsLatestAndStartDates(dailyTestId)
        def json = ['latestDate':redisService.("dailyTestsLatestDate"+"_"+dailyTestId),'startingDate':redisService.("dailyTestStartingDate"+"_"+dailyTestId)]
        render json as JSON
    }

    @Transactional
    def getDailyTestListForSite(){
        String siteId = params.siteId
        if(redisService.("dailyTestListForSite_"+siteId)==null) prepjoyService.getDailyTestListForSite(siteId)
        def json = ['dailyTests':redisService.("dailyTestListForSite_"+siteId)]
        render json as JSON

    }

    @Transactional
    def getDailyTestTypesForSite(){
        String siteId = params.siteId
        if(redisService.("dailyTestTypesForSite_"+siteId)==null) prepjoyService.getDailyTestTypesForSite(siteId)
        if(redisService.("dailyTestListForSite_"+siteId)==null) prepjoyService.getDailyTestListForSite(siteId)
        def json = ['dailyTestTypes':redisService.("dailyTestTypesForSite_"+siteId),'dailyTests':redisService.("dailyTestListForSite_"+siteId)]
        render json as JSON
    }

    @Transactional
    def getDailyTests(){
        String inputDate = params.dateInput
        String dailyTestId = params.dailyTestId

        if(params.realDailyTestDtlId!=null){
           DailyTestsDtl dailyTestsDtl = dataProviderService.getDailyTestsDtl(new Long(params.realDailyTestDtlId))
            SimpleDateFormat formatter = new SimpleDateFormat("dd-MM-yyyy");
            Date testCreatedDate =  utilService.convertDate(dailyTestsDtl.dateCreated, "UTC", "IST")
            inputDate = formatter.format(testCreatedDate);
            dailyTestId = ""+dailyTestsDtl.dailyTestId
        }

        if(redisService.("dailyTests_"+inputDate+"_"+dailyTestId)==null) prepjoyService.getDailyTests(inputDate,dailyTestId)


        def  jsonAnswers = redisService.("dailyTests_"+inputDate+"_"+dailyTestId)
        String realDailyTestDtlId = ""+redisService.("dailyTestsDtlId_"+inputDate+"_"+dailyTestId)

        def json =
                [
                        'results'     : jsonAnswers,
                        'status'      : jsonAnswers ? "OK" : "Nothing present",
                        'language1'   : "" ,
                        'language2'   : "",
                        'challengerName' : prepjoyService.getName(),
                        'challengerPlace' : prepjoyService.getPlace(),
                        'realDailyTestDtlId' : realDailyTestDtlId

                ]

        render json as JSON
    }

    @Transactional
    def quiz(){

    }
    def affiliation(){

    }

    @Transactional
    def getSiteLevelBooks(){
        String siteId = params.siteId
        if(redisService.("siteLevelBooks_"+siteId)==null) prepjoyService.getSiteLevelBooks(siteId)
        def json = ['siteLevelBooks':redisService.("siteLevelBooks_"+siteId)]
        render json as JSON

    }

    def currentaffairs(){
        setSiteInformation("currentaffairs")
        ['title': 'Current Affairs - Prepjoy']
    }
    def karnataka(){
        setSiteInformation("karnataka")
        ['title': 'Karnataka Jobs - Prepjoy']
    }

    def neet(){
        setSiteInformation("neet")
        ['title':'NEET and Medical entrances - Prepjoy']
    }
    def enggentrances(){
        setSiteInformation("enggentrances")
        ['title':'IIT/JEE and Engineering entrances - Prepjoy']
    }
    def ctet(){
        setSiteInformation("ctet")
        ['title':'Teaching Job Exams - Prepjoy']
    }
    def cacscma(){
        setSiteInformation("cacscma")
        ['title':'Prepjoy - CS CA CMA']
    }

    def alertNotifications(){
        dataNotificationService.prepjoyAlertNotification(params.alertType)
        render "success"
    }

    @Transactional
    def eBooks(){
        if(params.affiliationCd!=null) session.setAttribute("affiliationCd",params.affiliationCd)

        if(session.getAttribute("entryController")==null) setSiteInformation("prepjoy")
        else println("not going in")



        String title = null
        String seoDesc = null
        String publisherId

        if(params.publisherId!=null) publisherId = params.publisherId
        else if(params.publisher!=null){
            Publishers publishers = wsshopService.getPublisherByName(params.publisher.split("-").join(" "))
            publisherId = ""+publishers.id
            params.put("publisherId",publisherId)
        }


        int pageNo=0
        HashMap seo = sitemapService.getSEO(params,new Integer(1))
        params.put("level",seo.get("level"))
        params.put("syllabus",seo.get("syllabus"))
        params.put("grade",seo.get("grade"))

        if(params.pageNo!=null&&!"null".equals(params.pageNo)) pageNo = Integer.parseInt(params.pageNo)
        HashMap booksAndPublishers = wsshopService.getBooksList(params,new Integer(27),pageNo)
        def bannerList

        if(redisService.("bannerList_"+session['siteId'])==null) dataProviderService.getBanners(""+session['siteId'])
        bannerList = redisService.("bannerList_"+session['siteId'])

        ["title":seo.get("browserTitle"),booksList:booksAndPublishers,bannerList:bannerList,publisherId:publisherId, showPublishers:"true",
         seoDesc:seo.get("browserDescription"),keywords:seo.get("browserKeywords"),onPageTitle:seo.get("onPageTitle"),
         onPageDescription:seo.get("onPageDescription"),storeUrl:"/prepjoy/eBooks",publisherDescription:params.level==null&&seo.get("publisherDescription")!=null?seo.get("publisherDescription"):null,
         publisherName:seo.get("publisherName")]
    }

    @Transactional
    def setSiteInformation(String siteName){
        return siteManagerService.setPrepjoySiteInformation(siteName,session,response,servletContext)
    }

    def audioChat(){

    }

    def creator(){

    }
    def join(){

    }

    def generatePlayAccessCode(){
        render accessCodeService.generatePlayAccessCode(new Integer(123456),new Integer(332))
    }

    @Transactional @Secured(['ROLE_USER'])
    def getAccessCode(){

        // insert the challenge request
        Integer quizId = new Integer(-1)
        if(params.quizId!=null) quizId = new Integer(params.quizId)
        UserChallengesMst userChallengesMst = new UserChallengesMst(username: springSecurityService.currentUser.username,quizId:quizId,
        quizType: params.quizType,connectionCode: params.connectionCode,siteId: new Integer(params.siteId))
        userChallengesMst.save(failOnError: true, flush: true)
        String latestAccessCode
        if(redisService.("latestAccessCode_"+params.siteId)!=null) latestAccessCode = redisService.("latestAccessCode_"+params.siteId)
        else{
           List playAccessCodes = PlayAccessCodes.findAllBySiteId(new Integer(params.siteId), [sort:"id", order:"desc", max:1])
            if(playAccessCodes.size()>0&&playAccessCodes[0].actualCode!=null) {
                latestAccessCode = ""+playAccessCodes[0].actualCode
            }
            else latestAccessCode = ""+100000
        }
        String generatedCode = accessCodeService.generatePlayAccessCode(new Integer(latestAccessCode),new Integer(userChallengesMst.id.intValue()),
                                                                        new Integer(params.siteId))
        redisService.("connectionCode_"+params.siteId+"_"+generatedCode)= params.connectionCode
        redisService.("userChallengeId_"+params.siteId+"_"+generatedCode) = ""+userChallengesMst.id
        redisService.("quizId_"+params.siteId+"_"+generatedCode)= params.quizId
        redisService.("quizType_"+params.siteId+"_"+generatedCode)= params.quizType

        // send the notification if
        if("yes".equals(params.sameFriend)){
            dataNotificationService.sendSilentNotificationToUser(generatedCode,"",params.friendUsername,params.siteId,params.challengerName,params.bookId)
        }
        def json = ['challengeCode':generatedCode,userChallengeId:""+userChallengesMst.id]
        render json as JSON
    }

    @Transactional @Secured(['ROLE_USER'])
    def getConnectionCode() {

        String connectionCode = null
        String userChallengeId = null
        String quizId = null
        String quizType = null

        if(redisService.("connectionCode_"+params.siteId+"_"+params.challengeCode)!=null) {
            connectionCode = redisService.("connectionCode_"+params.siteId+"_"+params.challengeCode)
            userChallengeId = redisService.("userChallengeId_"+params.siteId+"_"+params.challengeCode)
            quizId = redisService.("quizId_"+params.siteId+"_"+params.challengeCode)
            quizType = redisService.("quizType_"+params.siteId+"_"+params.challengeCode)
        }
        else{
            PlayAccessCodes playAccessCodes = PlayAccessCodes.findByGeneratedCodeAndSiteId(params.challengeCode, new Integer(params.siteId))
            if(playAccessCodes!=null){
                UserChallengesMst userChallengesMst = UserChallengesMst.findByIdAndSiteId(playAccessCodes.challengeMstCode, new Integer(params.siteId))
                if(userChallengesMst!=null) {
                    connectionCode = userChallengesMst.connectionCode
                    userChallengeId = ""+userChallengesMst.id
                    quizId = ""+userChallengesMst.quizId
                    quizType = userChallengesMst.quizType
                    redisService.("connectionCode_"+params.siteId+"_"+params.challengeCode) = userChallengesMst.connectionCode
                    redisService.("userChallengeId_"+params.siteId+"_"+params.challengeCode) = ""+userChallengesMst.id
                    redisService.("quizId_"+params.siteId+"_"+params.challengeCode) = quizId
                    redisService.("quizType_"+params.siteId+"_"+params.challengeCode) = quizType
                }
            }
        }

        boolean hasAccess = false
    
        if("regular".equals(quizType)) {

            if(quizId!=null) {
                ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Integer(quizId))
                hasAccess = userManagementService.canSeeResourceCheck(resourceDtl,session,request,response)
            }
        }else{

            hasAccess = true
        }

         def json = [status:connectionCode==null?"Fail":"Success", connectionCode: connectionCode, userChallengeId:userChallengeId, hasAccess:hasAccess]
        render json as JSON
    }

    @Transactional @Secured(['ROLE_USER'])
    def getFullQuizDetails() {
        render prepjoyService.getFullQuizDetails(params.quizRecId) as JSON
    }

    @Transactional @Secured(['ROLE_USER'])
    def declinePlay() {

        UserChallengesMst userChallengesMst = UserChallengesMst.findById(new Integer(params.userChallengeId))
        if(userChallengesMst!=null){
            dataNotificationService.sendSilentNotificationToUser("declined to play",params.name,userChallengesMst.username,params.siteId,"","")
        }
        def json = [ 'status':'ok']
        render json as JSON
    }

    @Transactional
    def leaderBoard(){
        ['title': 'Leaderboard - Wonderslate',commonTemplate:"true"]
    }
    @Transactional
    def dailyTest(){
        String title = "Online Test Series - Olympiad, NEET, IIT-JEE, NCERT, UPSC"
        if(params.testName!=null) title = "Online Test - "+(""+params.testName).toUpperCase()
        ['title': title,commonTemplate:"true"]
    }
    @Transactional
    def quizAnalytics(){
        ['title': 'Quiz Analytics',commonTemplate:"true"]
    }

    @Transactional
    def history(){
        ['title': 'Quiz History',commonTemplate:"true"]
    }

    @Transactional
    def getRetestDetails(){
        String parentQuizRecId = params.parentQuizRecId
        String questionOptions = params.questionOptions

        QuizRecMst quizRecMst = QuizRecMst.findById(new Integer(parentQuizRecId))

        def json =
                [
                        'results'     : prepjoyService.getRetestDetails(parentQuizRecId,questionOptions),
                        'status'      : "OK" ,
                        'language1'   : "" ,
                        'language2'   : "",
                        'challengerName' : prepjoyService.getName(),
                        'challengerPlace' : prepjoyService.getPlace(),
                        'realDailyTestDtlId' : quizRecMst.realDailyTestDtlId,
                        'resId':quizRecMst.resId,
                        'parentQuizRecId':parentQuizRecId,
                        'questionOptions':questionOptions

                ]

        render json as JSON
    }

    @Transactional
    def getRetestDetailsForFlashcards(){
        String parentQuizRecId = params.parentQuizRecId
        String questionOptions = params.questionOptions

        def json = ["keyValues"      :  prepjoyService.getRetestDetailsForFlashcards(parentQuizRecId,questionOptions),
                    "resId": null, "resType": "Multiple Choice Questions", "resourceName": "Revision  ",
                    "canEdit"        :  "false", description: "",
                    dateCreated      : null, fastestTime: null,
                    createdBy        :  "",
                    createdByUsername: "",
                    profilePic       : ""]
        render json as JSON

    }


    @Transactional
    def getImproveQuestionsForMCQs(){
        String subject = params.subject
        String username = springSecurityService.currentUser.username
        String limit=10
        if(params.limit!=null) limit=params.limit


        def json =
                [
                        'results'     : prepjoyService.getImproveQuestionsForMCQs(subject,username,limit),
                        'status'      : "OK" ,
                        'language1'   : "" ,
                        'language2'   : "",
                        'challengerName' : prepjoyService.getName(),
                        'challengerPlace' : prepjoyService.getPlace(),
                        'realDailyTestDtlId' : "-1",
                        'resId':null

                ]

        render json as JSON


    }
    @Transactional
    def getImproveQuestions(){
        String subject = params.subject
        String username = springSecurityService.currentUser.username
        String limit=10
        if(params.limit!=null) limit=params.limit


        def json = ["keyValues"      : prepjoyService.getImproveQuestions(subject,username,limit),
                    "resId": null, "resType": "Multiple Choice Questions", "resourceName": "Revision - "+subject,
                    "canEdit"        :  "false", description: "",
                    dateCreated      : null, fastestTime: null,
                    createdBy        :  "",
                    createdByUsername: "",
                    profilePic       : ""]
        render json as JSON


    }

    def dailyCurrentAffairs(){
        Integer siteId = utilService.getSiteId(request,session)
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        String currentAffairsType = "Main"
        if(siteMst.currentAffairsType!=null&&!"".equals(siteMst.currentAffairsType)) currentAffairsType = siteMst.currentAffairsType
        else{
            SiteDtl siteDtl = dataProviderService.getSiteDtl(siteId)
            if(siteDtl!=null&&"true".equals(siteDtl.digitalLibraryLandingPage)) currentAffairsType="KICX"
        }
        ['title': 'Current Affairs',commonTemplate:"true",currentAffairsType:currentAffairsType]
    }

    @Secured(['ROLE_USER']) @Transactional
    def admin(){
        Integer siteId = utilService.getSiteId(request,session)
        println(siteId)
        if(userManagementService.isValidSession(springSecurityService.currentUser.username,session.getId())) {
            User user = session["userdetails"]
            boolean showPublisherControls=false,hasSalesAccess=false,wsAdmin=false,instituteAdmin=false,showPublisherAdminControls=false,informationAdmin=false,
                    guestUser=false,customerSupport=false,groupsAdmin=false,salesSupport=false,libraryUserUploader=false,externalSalesAccess=false;

            // for publisher admin controlls
            if(session["userdetails"].publisherId!=null&&!session["isInstitutePublisher"]&&user.authorities.any {

                it.authority == "ROLE_WS_CONTENT_ADMIN"
            }) showPublisherAdminControls = true

            // for publisher content uploaders
            if(session["userdetails"].publisherId!=null&&!session["isInstitutePublisher"]&&user.authorities.any {
                it.authority == "ROLE_BOOK_CREATOR"
            }) showPublisherControls = true
            //for wonderslate content creators
            else if(session["userdetails"].publisherId==null &&user.authorities.any {
                it.authority == "ROLE_WS_CONTENT_ADMIN"
            }) wsAdmin = true

            def existCount=session.getAttribute("groupWallNotificationCount")
            if(redisService.("groupWallId_"+session['siteId']+"_latestPostCount")==null || redisService.("groupWallId_"+session['siteId']+"_latestPostCount")=="null")
            {
                groupsService.getlatestPostCountForGroupWall(siteId)
            }
            if(existCount==null) session.setAttribute("groupWallNotificationCount",redisService.("groupWallId_"+session['siteId']+"_latestPostCount"))
            session.setAttribute("groupWallPendingCount",(new Integer(redisService.("groupWallId_"+session['siteId']+"_latestPostCount"))-new Integer(session.getAttribute("groupWallNotificationCount")))>0?(new Integer(redisService.("groupWallId_"+session['siteId']+"_latestPostCount"))-new Integer(session.getAttribute("groupWallNotificationCount"))):"0")

            //institutes for user
            List userInstitutes
            if(session["userInstitutes"]!=null) userInstitutes = session["userInstitutes"]
            else    {
                userInstitutes=userManagementService.getInstitutesForUser(1,utilService.getIPAddressOfClient(request))
                if(userInstitutes.size()>1){
                    for(int i=0;i<userInstitutes.size();i++){
                        if(!("Default".equals(""+userInstitutes[i].batchName))) userInstitutes.remove(i--)
                    }
                }
                session["userInstitutes"] = userInstitutes
            };
            //sales access
            if(user.authorities.any {
                it.authority == "ROLE_FINANCE" || it.authority == "ROLE_AFFILIATION_SALES"
            }) {
                hasSalesAccess = true
            }

            //instituteAdmin users - eClass+
            if(user.authorities.any {
                it.authority == "ROLE_INSTITUTE_ADMIN"||it.authority == "ROLE_INSTITUTE_REPORT_MANAGER"
            }) {
                instituteAdmin = true
            }

            //library user uploader
            if(user.authorities.any {
                it.authority == "ROLE_LIBRARY_USER_UPLOADER"
            }) {
                libraryUserUploader = true
            }
            if(user.username.contains("1_cookie_"))guestUser=true
            //information admin users
            if(user.authorities.any {
                it.authority == "ROLE_INFORMATION_ADMIN"
            }) {
                informationAdmin = true
            }

            //customer support access
            if(user.authorities.any {
                it.authority == "ROLE_CUSTOMER_SUPPORT"
            }) {
                customerSupport = true
            }

            //sales team access
            if(user.authorities.any {
                it.authority == "ROLE_WS_SALES_TEAM"
            }) {
                salesSupport = true
            }

            //group admin control
            if(user.authorities.any {
                it.authority == "ROLE_WS_GROUP_ADMIN"
            }) {
                groupsAdmin = true
            }

            //external sales report viewer
            //information admin users
            if(user.authorities.any {
                it.authority == "ROLE_EXTERNAL_SALES_VIEWER"
            }) {
                externalSalesAccess = true
            }
            def groupWallId=null
            if(redisService.("defaultGroupWallId_1")==null){
                KeyValueMst keyValueMst= KeyValueMst.findByKeyNameAndSiteId("groupWallId",1)
                if(keyValueMst!=null ) redisService.("defaultGroupWallId_1")=keyValueMst.keyValue
            }
            groupWallId = redisService.("defaultGroupWallId_1")

            ["title": "Admin - Prepjoy",userInstitutes:userInstitutes,showPublisherControls:showPublisherControls,hasSalesAccess:hasSalesAccess,
             wsAdmin:wsAdmin,instituteAdmin:instituteAdmin, commonTemplate:"true", showPublisherAdminControls:showPublisherAdminControls,informationAdmin:informationAdmin,guestUser:guestUser,
             customerSupport:customerSupport,groupWallId:groupWallId,groupsAdmin:groupsAdmin,salesSupport:salesSupport,libraryUserUploader:libraryUserUploader,externalSalesAccess:externalSalesAccess]
        }else{
            Cookie cookie = new Cookie("SimulError", "Fail")
            cookie.path = "/"
            WebUtils.retrieveGrailsWebRequest().getCurrentResponse().addCookie(cookie)
            redirect([uri: '/logoff'])
        }
    }


}
